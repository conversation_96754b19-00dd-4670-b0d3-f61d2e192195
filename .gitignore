# Rust Build Artifacts
/target/
**/*.rs.bk
*.pdb
releases/

# Configuration Files (Keep config.example.toml)
config.toml
*.local.toml
.env
.env.local
.env.*.local
.env.development
.env.staging
.env.production

# Logs and Runtime Data
*.log
logs/
log/
*.pid
*.seed
*.pid.lock

# IDE and Editor Files
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.iml
*.ipr
*.iws
*.swp
*.swo
*~

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
tmp/
temp/
*.tmp
*.temp

# Build Artifacts
/dist/
/build/
*.exe
*.dmg
*.app
*.deb
*.rpm

# Performance and Profiling Files
*.prof
*.cpuprofile
*.heapprofile
perf.data*
flamegraph.svg

# Coverage and Test Results
coverage/
*.lcov
/test-results/

# Backup Files
*.bak
*.backup
*.old

# Local Development
*.local
.local/

# Documentation Build
/docs/_build/
/docs/build/

# Emacs
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*
