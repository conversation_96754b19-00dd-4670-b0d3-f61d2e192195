# Shred Forwarder Configuration Example
# Copy this file to config.toml and modify as needed

# ERPC Configuration (Required)
[erpc]
# ERPC endpoint URL - REQUIRED
endpoint = "https://shreds-far-point.erpc.global"

# Connection settings
max_retries = 5
initial_retry_delay_ms = 1000
max_retry_delay_ms = 30000
auto_reconnect = true

# Application Configuration
[app]
# Application name (auto-detected from Cargo.toml)
name = "shred-forwarder"
version = "0.1.0"
environment = "development"  # development, staging, production

# Pre-configured accounts to monitor (Required)
# Only transactions from these accounts will be streamed
accounts = [
    "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
    # Add more account addresses here
]

# gRPC Server Configuration
[grpc]
# Bind address (default: 127.0.0.1)
bind_address = "127.0.0.1"
port = 50051

# Performance optimizations
tcp_nodelay = true
tcp_keepalive = true
http2_keepalive_interval = 30
http2_keepalive_timeout = 5
http2_adaptive_window = true
max_concurrent_streams = 1000

# Logging Configuration
[logging]
# Log level: trace, debug, info, warn, error
level = "info"
# Log format: compact, pretty, json
format = "compact"

# Performance Optimization Settings
[performance]

# Memory Pool Configuration
[performance.memory_pools]
# Buffer pool settings
buffer_pool_initial_size = 100
buffer_pool_max_size = 1000

# Pool statistics logging
enable_pool_stats_logging = true
stats_log_interval_seconds = 60

# Metrics Configuration
[performance.metrics]
# Enable performance metrics collection
enable_metrics = true
# Enable detailed metrics logging
enable_detailed_logging = true
# Metrics logging interval in seconds
metrics_log_interval_seconds = 30
# Enable processing time tracking
enable_processing_timers = true

# CPU Affinity Configuration
[performance.cpu_affinity]
# Enable CPU affinity for performance optimization
enable_cpu_affinity = false
# CPU core IDs for gRPC server threads (empty = auto-detect)
grpc_server_core_ids = []
# CPU core IDs for ERPC client threads (empty = auto-detect)
erpc_client_core_ids = []
