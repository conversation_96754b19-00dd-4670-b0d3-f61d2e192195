use solana_transaction::versioned::VersionedTransaction;

use crate::grpc::proto::{
    CompiledInstruction as ProtoCompiledInstruction, LegacyMessage as ProtoLegacyMessage,
    MessageHeader as ProtoMessageHeader, VersionedMessage as ProtoVersionedMessage,
    VersionedTransaction as ProtoVersionedTransaction,
};
use crate::types::GrpcResult;

pub fn convert_to_proto_transaction(transaction: &VersionedTransaction) -> GrpcResult<ProtoVersionedTransaction> {
    let signatures = transaction.signatures.iter().map(|sig| sig.as_ref().to_vec()).collect();

    let static_account_keys = transaction.message.static_account_keys();
    let mut account_keys = Vec::with_capacity(static_account_keys.len());
    for key in static_account_keys.iter() {
        account_keys.push(key.as_ref().to_vec());
    }

    let message_instructions = transaction.message.instructions();
    let mut instructions = Vec::with_capacity(message_instructions.len());
    for inst in message_instructions.iter() {
        instructions.push(ProtoCompiledInstruction {
            program_id_index: inst.program_id_index as u32,
            accounts: inst.accounts.iter().map(|&idx| idx as u32).collect(),
            data: inst.data.clone(),
        });
    }

    let header = transaction.message.header();
    let proto_header = ProtoMessageHeader {
        num_required_signatures: header.num_required_signatures as u32,
        num_readonly_signed_accounts: header.num_readonly_signed_accounts as u32,
        num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u32,
    };

    let recent_blockhash = transaction.message.recent_blockhash().as_ref().to_vec();

    let legacy_message = ProtoLegacyMessage {
        header: Some(proto_header),
        account_keys,
        recent_blockhash,
        instructions,
    };

    let message = ProtoVersionedMessage {
        message_type: Some(crate::grpc::proto::versioned_message::MessageType::Legacy(
            legacy_message,
        )),
    };

    Ok(ProtoVersionedTransaction {
        signatures,
        message: Some(message),
    })
}
