use tracing_subscriber::fmt;

use crate::config::LoggingConfig;
use crate::types::{ConfigError, ConfigResult};

pub fn init_logging(config: &LoggingConfig) -> ConfigResult<()> {
    // Initialize based on format preference
    match config.format {
        crate::config::logging::LogFormat::Json => {
            let subscriber = fmt().json().with_max_level(convert_log_level(&config.level)).finish();
            tracing::subscriber::set_global_default(subscriber)
        }
        crate::config::logging::LogFormat::Pretty => {
            let subscriber = fmt().pretty().with_max_level(convert_log_level(&config.level)).finish();
            tracing::subscriber::set_global_default(subscriber)
        }
        crate::config::logging::LogFormat::Compact => {
            let subscriber = fmt()
                .compact()
                .with_max_level(convert_log_level(&config.level))
                .finish();
            tracing::subscriber::set_global_default(subscriber)
        }
    }
    .map_err(|e| ConfigError::InvalidValue {
        key: "logging.init".to_string(),
        value: e.to_string(),
    })?;

    Ok(())
}

fn convert_log_level(level: &crate::config::logging::LogLevel) -> tracing::Level {
    match level {
        crate::config::logging::LogLevel::Trace => tracing::Level::TRACE,
        crate::config::logging::LogLevel::Debug => tracing::Level::DEBUG,
        crate::config::logging::LogLevel::Info => tracing::Level::INFO,
        crate::config::logging::LogLevel::Warn => tracing::Level::WARN,
        crate::config::logging::LogLevel::Error => tracing::Level::ERROR,
    }
}

pub fn log_startup_info(config: &crate::config::MainConfig) {
    tracing::info!(
        app_name = %config.app.name,
        app_version = %config.app.version,
        environment = ?config.app.environment,
        grpc_address = %format!("{}:{}", config.grpc.bind_address, config.grpc.port),
        erpc_endpoint = %config.erpc.endpoint,
        "Application starting"
    );
}
