use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigResult};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub name: String,
    pub version: String,
    pub environment: Environment,
    pub accounts: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Environment {
    Development,
    Staging,
    Production,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            name: env!("CARGO_PKG_NAME").to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            environment: Environment::Development,
            accounts: vec![],
        }
    }
}

impl AppConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.name.is_empty() {
            return Err(ConfigError::InvalidValue {
                key: "name".to_string(),
                value: self.name.clone(),
            });
        }

        Ok(())
    }

    pub fn get_accounts(&self) -> &[String] {
        &self.accounts
    }
}
