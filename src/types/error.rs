use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, Debug)]
pub enum ConfigError {
    #[error("Missing required configuration: {key}")]
    MissingRequired { key: String },

    #[error("Invalid configuration value for {key}: {value}")]
    InvalidValue { key: String, value: String },

    #[error("Failed to parse configuration: {source}")]
    ParseError {
        #[from]
        source: config::ConfigError,
    },
}

#[derive(<PERSON><PERSON><PERSON>, Debug)]
pub enum ErpcError {
    #[error("Connection failed: {message}")]
    ConnectionFailed { message: String },

    #[error("Subscription failed: {message}")]
    SubscriptionFailed { message: String },

    #[error("Stream disconnected: {message}")]
    StreamDisconnected { message: String },

    #[error("Invalid response: {message}")]
    InvalidResponse { message: String },

    #[error("Invalid request: {message}")]
    InvalidRequest { message: String },
}

#[derive(<PERSON><PERSON><PERSON>, Debug)]
pub enum GrpcError {
    #[error("Server startup failed: {message}")]
    ServerStartFailed { message: String },

    #[error("Service error: {message}")]
    Service { message: String },
}
