.PHONY: build run clean fmt lint build-release build-mac build-linux build-all release

build:
	cargo build

build-release:
	cargo build --release

build-mac:
	cargo build --release --target aarch64-apple-darwin

build-linux:
	cargo build --release --target x86_64-unknown-linux-gnu

build-all: build-mac build-linux
	@echo "Built for all targets:"
	@echo "  macOS M1: target/aarch64-apple-darwin/release/shred-forwarder"
	@echo "  Linux x64: target/x86_64-unknown-linux-gnu/release/shred-forwarder"

release: build-all
	@mkdir -p releases
	@cp target/aarch64-apple-darwin/release/shred-forwarder releases/shred-forwarder-macos-arm64
	@cp target/x86_64-unknown-linux-gnu/release/shred-forwarder releases/shred-forwarder-linux-x64
	@echo "Release binaries created in releases/ directory:"
	@ls -la releases/

run:
	cargo run

clean:
	cargo clean

fmt:
	cargo fmt

lint:
	cargo clippy
